# Source Code

This directory contains the source code for the AI SDR Agent application.

## Structure

- **`backend/`** - Serverless backend services built with AWS Lambda
- **`frontend/`** - React.js frontend application
- **`shared/`** - Shared utilities and types used across frontend and backend

## Getting Started

### Backend Development

```bash
cd backend
npm install
serverless offline
```

### Frontend Development

```bash
cd frontend
npm install
npm start
```

## Architecture

The application follows a microservices architecture with:

- **Authentication Service**: User login and JWT management
- **Leads Service**: Lead management and CRUD operations
- **Outreach Service**: Email, SMS, and LinkedIn communication
- **Analytics Service**: Dashboard metrics and reporting
- **Integrations Service**: External API integrations (CRM, AI services)

## Technology Stack

- **Backend**: Node.js, AWS Lambda, DynamoDB, API Gateway
- **Frontend**: React.js, TypeScript, Tailwind CSS, Redux Toolkit
- **AI Services**: Relevance AI, Google Gemini API
- **Communication**: <PERSON><PERSON><PERSON>, <PERSON>wilio, LinkedIn API

## Development Guidelines

1. Follow the coding standards outlined in `/docs/CONTRIBUTING.md`
2. Write tests for all new features
3. Use TypeScript for type safety
4. Follow the established project structure
5. Document your code and APIs

## Testing

```bash
# Run all tests
npm test

# Frontend tests
cd frontend && npm test

# Backend tests
cd backend && npm test
```

## Deployment

```bash
# Deploy backend
cd backend && serverless deploy

# Build and deploy frontend
cd frontend && npm run build
```

For detailed deployment instructions, see the main README.md file.
