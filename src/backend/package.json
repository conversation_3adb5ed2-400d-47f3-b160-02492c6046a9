{"name": "ai-sdr-backend", "version": "1.0.0", "description": "Backend services for AI SDR Agent", "main": "index.js", "scripts": {"start": "serverless offline", "deploy": "serverless deploy", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix"}, "dependencies": {"aws-lambda": "^1.0.7", "aws-sdk": "^2.1400.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "joi": "^17.9.0", "uuid": "^9.0.0"}, "devDependencies": {"serverless": "^3.30.0", "serverless-offline": "^12.0.0", "serverless-webpack": "^5.11.0", "webpack": "^5.80.0", "jest": "^29.5.0", "eslint": "^8.40.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "typescript": "^5.0.0"}, "author": "GEMDevEng", "license": "MIT"}