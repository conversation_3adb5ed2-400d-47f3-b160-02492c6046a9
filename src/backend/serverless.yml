service: ai-sdr-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region, 'us-east-1'}
  stage: ${opt:stage, 'dev'}
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    DYNAMODB_TABLE_PREFIX: ${self:service}-${self:provider.stage}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
      Resource:
        - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:provider.environment.DYNAMODB_TABLE_PREFIX}-*"
    - Effect: Allow
      Action:
        - sqs:SendMessage
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
      Resource:
        - "arn:aws:sqs:${self:provider.region}:*:${self:provider.environment.DYNAMODB_TABLE_PREFIX}-*"

functions:
  # Authentication Service
  auth:
    handler: services/auth/handler.main
    events:
      - http:
          path: auth/{proxy+}
          method: ANY
          cors: true

  # Leads Service
  leads:
    handler: services/leads/handler.main
    events:
      - http:
          path: leads/{proxy+}
          method: ANY
          cors: true

  # Outreach Service
  outreach:
    handler: services/outreach/handler.main
    events:
      - http:
          path: outreach/{proxy+}
          method: ANY
          cors: true

  # Analytics Service
  analytics:
    handler: services/analytics/handler.main
    events:
      - http:
          path: analytics/{proxy+}
          method: ANY
          cors: true

  # Integrations Service
  integrations:
    handler: services/integrations/handler.main
    events:
      - http:
          path: integrations/{proxy+}
          method: ANY
          cors: true

resources:
  Resources:
    # DynamoDB Tables
    LeadsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-leads
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-users
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

    JobsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.DYNAMODB_TABLE_PREFIX}-jobs
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST

plugins:
  - serverless-offline
  - serverless-webpack
