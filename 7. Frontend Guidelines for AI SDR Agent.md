# Frontend Guidelines for AI SDR Agent

## 1. Introduction
This document provides guidelines for developing the frontend of the AI Sales Development Representative (SDR) Agent, a web-based application designed to streamline lead generation, qualification, outreach, and analytics for mining equipment suppliers. The frontend ensures an intuitive and efficient user experience for sales representatives and administrators. These guidelines cover design principles, UI components, layout, styling, interaction patterns, accessibility, performance, testing, and recommended tools, aligning with the project’s technical architecture and user needs.

## 2. Design Principles
The frontend design should adhere to the following core principles:

- **User-Centric**: Focus on the workflows of sales representatives and administrators, such as managing leads and executing outreach.
- **Consistency**: Use uniform UI elements and interactions throughout the application.
- **Simplicity**: Avoid clutter and keep the interface straightforward to minimize user effort.
- **Responsiveness**: Ensure compatibility across desktops, laptops, and tablets.
- **Accessibility**: Make the application usable for individuals with disabilities.
- **Performance**: Prioritize fast load times and smooth interactions.

## 3. UI Components
Standardize UI components for consistency and efficiency. Consider adopting a library like [Material-UI](https://mui.com/) or [Ant Design](https://ant.design/).

### 3.1 Buttons
- **Appearance**: Use descriptive labels (e.g., "Send Email"), a minimum width of 100px, and adequate padding.
- **States**: Define styles for hover, active, and disabled states (e.g., blue for primary actions, gray for secondary).
- **Icons**: Pair with icons where applicable (e.g., a plus icon for "Add Lead").

### 3.2 Forms
- **Labels and Placeholders**: Include both for every input field.
- **Validation**: Show inline messages (e.g., "Please enter a valid email").
- **Grouping**: Organize related fields using fieldsets.

### 3.3 Tables
- **Headers**: Enable sorting with visible indicators (e.g., arrows).
- **Pagination**: Support large datasets with page size options (e.g., 10, 25, 50).
- **Actions**: Add per-row buttons (e.g., "Edit", "Delete").

### 3.4 Modals
- **Usage**: Use for focused tasks (e.g., confirming deletions).
- **Design**: Include a title, description, and action buttons (e.g., "Confirm", "Cancel").

## 4. Layout and Navigation
Provide a clear and responsive structure for navigation and content.

- **Sidebar Navigation**: Implement a collapsible sidebar for sections like Dashboard, Leads, Campaigns, and Settings.
- **Top Bar**: Feature a logo, user profile dropdown, and notifications.
- **Breadcrumbs**: Show navigation depth (e.g., Home > Leads > Lead Details).
- **Responsive Design**: Collapse the sidebar into a hamburger menu on smaller screens.

## 5. Styling and Theming
Establish a cohesive visual style tied to the mining industry.

- **Color Palette**:
  - Primary: #1E3A8A (deep blue)
  - Secondary: #F59E0B (gold)
  - Background: #F3F4F6 (light gray)
  - Text: #1F2937 (dark gray)
- **Typography**:
  - Font Family: "Inter", sans-serif
  - Headings: 24px (H1), 20px (H2), 16px (H3)
  - Body Text: 14px
- **Spacing**: Use a scale (e.g., 8px, 16px, 24px) for margins and padding.
- **Icons**: Adopt a set like [Heroicons](https://heroicons.com/).

## 6. Interaction Patterns
Enhance usability with consistent interaction feedback.

- **Loading Indicators**: Show spinners for operations like data imports.
- **Feedback Messages**: Use toast notifications (e.g., "Campaign started successfully").
- **Tooltips**: Add for clarity on icons or features.
- **Keyboard Navigation**: Support tabbing and shortcuts.

## 7. Accessibility
Follow [WCAG 2.1](https://www.w3.org/WAI/standards-guidelines/wcag/) standards.

- **Semantic HTML**: Use proper tags (e.g., `<nav>`, `<button>`).
- **Alt Text**: Describe all images and icons.
- **Color Contrast**: Maintain a 4.5:1 ratio for text readability.
- **Focus Management**: Ensure visible focus states for interactive elements.

## 8. Performance
Optimize for speed and responsiveness.

- **Lazy Loading**: Implement with React.lazy for dynamic imports.
- **Image Optimization**: Compress images and use responsive formats.
- **Bundle Size**: Minimize with tree-shaking and code splitting.
- **Caching**: Cache static assets and API responses.

## 9. Testing
Validate functionality and usability through rigorous testing.

- **Unit Tests**: Use [Jest](https://jestjs.io/) and [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/).
- **End-to-End Tests**: Test flows (e.g., lead import) with [Cypress](https://www.cypress.io/).
- **Usability Testing**: Gather feedback from real users.
- **Cross-Browser Testing**: Verify on Chrome, Firefox, and Safari.

## 10. Tools and Technologies
Leverage these tools for development efficiency.

- **Framework**: [React.js](https://reactjs.org/) (v18.x)
- **State Management**: [Redux](https://redux.js.org/) or [React Context API](https://reactjs.org/docs/context.html)
- **API Calls**: [Axios](https://axios-http.com/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) or [styled-components](https://styled-components.com/)
- **Code Quality**: [ESLint](https://eslint.org/), [Prettier](https://prettier.io/)
- **Version Control**: [Git](https://git-scm.com/) and [GitHub](https://github.com/)
- **Documentation**: [JSDoc](https://jsdoc.app/)

## 11. Collaboration and Version Control
- **Branching**: Use feature branches with pull requests.
- **Commits**: Write descriptive messages (e.g., "Fix table sorting").
- **Reviews**: Require at least one reviewer per change.

## 12. Documentation
- **Components**: Document with JSDoc (props, methods).
- **User Guides**: Include in-app help or tooltips.
- **API Integration**: Detail frontend-backend interactions.

## Conclusion
These guidelines ensure the AI SDR Agent frontend is intuitive, performant, and maintainable, supporting mining equipment suppliers in automating their sales processes effectively.