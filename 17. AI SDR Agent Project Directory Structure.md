# AI SDR Agent Project Directory Structure

## 1. Introduction
The AI Sales Development Representative (SDR) Agent is a modular SaaS platform designed to automate lead generation, qualification, outreach, and analytics for mining equipment suppliers. This document outlines the directory structure for the project, ensuring modularity, scalability, and ease of collaboration for a team of five junior developers. The structure supports a serverless backend, a React.js frontend, and integrations with external services, aligning with the project's phased development approach (Core Outreach, Adding Channels & Enrichment, Full AI Agents & Analytics) and the SPIDR methodology for task breakdown.

## 2. Directory Structure
The project adopts a monorepo structure to centralize all components, facilitating version control and collaboration. The structure is designed to support concurrent development by assigning distinct modules to each developer while centralizing shared resources to avoid duplication.

```markdown
ai-sdr-agent/
├── backend/
│   ├── services/
│   │   ├── auth/
│   │   │   ├── index.js
│   │   │   ├── handler.js
│   │   │   └── tests/
│   │   │       ├── unit/
│   │   │       └── integration/
│   │   ├── leads/
│   │   │   ├── index.js
│   │   │   ├── handler.js
│   │   │   └── tests/
│   │   │       ├── unit/
│   │   │       └── integration/
│   │   ├── outreach/
│   │   │   ├── index.js
│   │   │   ├── handler.js
│   │   │   └── tests/
│   │   │       ├── unit/
│   │   │       └── integration/
│   │   ├── analytics/
│   │   │   ├── index.js
│   │   │   ├── handler.js
│   │   │   └── tests/
│   │   │       ├── unit/
│   │   │       └── integration/
│   │   └── integrations/
│   │       ├── index.js
│   │       ├── handler.js
│   │       └── tests/
│   │           ├── unit/
│   │           └── integration/
│   ├── shared/
│   │   ├── utils/
│   │   │   ├── api.js
│   │   │   └── helpers.js
│   │   └── models/
│   │       ├── lead.js
│   │       └── campaign.js
│   ├── serverless.yml
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── LeadList.jsx
│   │   │   ├── LeadDetails.jsx
│   │   │   ├── CampaignDashboard.jsx
│   │   │   └── AuthForm.jsx
│   │   ├── pages/
│   │   │   ├── Login.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── Leads.jsx
│   │   │   ├── Campaigns.jsx
│   │   │   └── Settings.jsx
│   │   ├── styles/
│   │   │   ├── global.css
│   │   │   └── tailwind.css
│   │   ├── App.jsx
│   │   ├── index.jsx
│   │   └── tailwind.config.js
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   └── package.json
├── infrastructure/
│   └── cloudformation/
│       ├── dynamo.yml
│       └── sqs.yml
├── docs/
│   ├── README.md
│   ├── CONTRIBUTING.md
│   ├── API_DOCS.md
│   └── USER_GUIDE.md
├── tests/
│   ├── unit/
│   │   ├── backend/
│   │   └── frontend/
│   ├── integration/
│   │   ├── backend/
│   │   └── frontend/
│   └── e2e/
│       ├── cypress/
│       │   ├── integration/
│       │   └── support/
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── deploy.yml
├── config/
│   └── .env.example
├── scripts/
│   ├── ai-agents/
│   │   ├── jules-config.json
│   │   ├── cline-Line-setup.sh
│   │   └── refact-config.json
│   ├── deploy.sh
│   └── test-all.sh
├── LICENSE
├── .gitignore
└── README.md
```

## 3. Directory Structure Details

### 3.1 Root Directory (`ai-sdr-agent/`)
- **Purpose**: Centralizes all project files and subdirectories.
- **Key Files**:
  - `LICENSE`: Project license agreement.
  - `.gitignore`: Excludes files like `node_modules/` and `.env` from version control.
  - `README.md`: Project overview and setup instructions.

### 3.2 Backend (`backend/`)
- **Purpose**: Contains serverless microservices and shared utilities for the backend.
- **Subdirectories and Files**:
  - **services/**: Individual microservices for authentication, lead management, outreach, analytics, and integrations.
    - **auth/**: Authentication service logic.
      - `index.js`: Main entry point for the service.
      - `handler.js`: Lambda function handler.
      - `tests/`: Unit and integration tests.
    - **leads/**: Lead management service logic.
    - **outreach/**: Outreach service logic.
    - **analytics/**: Analytics service logic.
    - **integrations/**: External integration logic.
  - **shared/**:
    - **utils/**: Shared utility functions (e.g., API helpers).
    - **models/**: Data models (e.g., Lead, Campaign).
  - **serverless.yml**: Serverless Framework configuration for AWS Lambda and API Gateway.
  - **package.json**: Backend dependencies and scripts.

### 3.3 Frontend (`frontend/`)
- **Purpose**: Contains the React.js application for the user interface.
- **Subdirectories and Files**:
  - **src/**:
    - **components/**: Reusable UI components (e.g., `LeadList.jsx`).
    - **pages/**: Page-level components (e.g., `Login.jsx`, `Dashboard.jsx`).
    - **styles/**: CSS files, including Tailwind CSS configurations.
    - **App.jsx**: Main application component.
    - **index.jsx**: Application entry point.
    - **tailwind.config.js**: Tailwind CSS configuration.
  - **public/**: Static assets (e.g., `index.html`, `favicon.ico`).
  - **package.json**: Frontend dependencies and scripts.

### 3.4 Infrastructure (`infrastructure/`)
- **Purpose**: Stores infrastructure-as-code configurations for AWS resources.
- **Subdirectories and Files**:
  - **cloudformation/**:
    - `dynamo.yml`: DynamoDB table configurations.
    - `sqs.yml`: SQS queue configurations.

### 3.5 Documentation (`docs/`)
- **Purpose**: Centralizes project documentation.
- **Files**:
  - `README.md`: Project overview and setup instructions.
  - `CONTRIBUTING.md`: Guidelines for contributors.
  - `API_DOCS.md`: API endpoint documentation.
  - `USER_GUIDE.md`: User-facing documentation.

### 3.6 Tests (`tests/`)
- **Purpose**: Stores centralized test suites for the project.
- **Subdirectories**:
  - **unit/**: Unit tests for backend and frontend code.
  - **integration/**: Integration tests for inter-service interactions.
  - **e2e/**: End-to-end tests using [Cypress]([invalid url, do not cite]).
    - **cypress/**: Cypress test configurations and scripts.

### 3.7 GitHub Workflows (`.github/`)
- **Purpose**: Contains CI/CD pipeline configurations.
- **Subdirectories and Files**:
  - **workflows/**:
    - `ci.yml`: Continuous integration pipeline for testing.
    - `deploy.yml`: Deployment pipeline for AWS.

### 3.8 Configuration (`config/`)
- **Purpose**: Stores configuration templates.
- **Files**:
  - `.env.example`: Example environment variables file (actual `.env` excluded from version control).

### 3.9 Scripts (`scripts/`)
- **Purpose**: Contains utility scripts for development and deployment.
- **Subdirectories and Files**:
  - **ai-agents/**:
    - `jules